/**
 * 模拟API服务器
 * 用于开发环境测试API接口
 * 运行命令: node mock/server.js
 */

const express = require('express')
const cors = require('cors')
const app = express()
const PORT = 3000

// 中间件
app.use(cors())
app.use(express.json())

// 模拟数据
const mockData = {
  realtime: {
    totalVisitors: 1234567,
    todayVisitors: 8932,
    revenue: 2345678.90,
    satisfaction: 4.8,
    timestamp: new Date().toISOString()
  },
  regions: [
    { region: '济南市', visitors: 123456, revenue: 234567.89, growth: 12.5 },
    { region: '青岛市', visitors: 234567, revenue: 345678.90, growth: 8.3 },
    { region: '烟台市', visitors: 156789, revenue: 267890.12, growth: 15.2 },
    { region: '威海市', visitors: 98765, revenue: 187654.32, growth: 6.7 },
    { region: '泰安市', visitors: 187654, revenue: 298765.43, growth: 9.8 }
  ],
  hotspots: [
    { name: '泰山', visitors: 45678, rating: 4.9, location: '泰安市' },
    { name: '崂山', visitors: 34567, rating: 4.7, location: '青岛市' },
    { name: '蓬莱阁', visitors: 23456, rating: 4.6, location: '烟台市' },
    { name: '刘公岛', visitors: 19876, rating: 4.8, location: '威海市' },
    { name: '趵突泉', visitors: 28765, rating: 4.5, location: '济南市' }
  ],
  visitorTrend: [
    { id: 1, name: '1月', value: 123456, trend: 'up' },
    { id: 2, name: '2月', value: 134567, trend: 'up' },
    { id: 3, name: '3月', value: 145678, trend: 'up' },
    { id: 4, name: '4月', value: 156789, trend: 'up' },
    { id: 5, name: '5月', value: 167890, trend: 'up' },
    { id: 6, name: '6月', value: 178901, trend: 'up' }
  ],
  revenueStats: [
    { id: 1, name: '门票收入', value: 1234567, percentage: 45 },
    { id: 2, name: '酒店收入', value: 987654, percentage: 35 },
    { id: 3, name: '餐饮收入', value: 543210, percentage: 20 }
  ],
  satisfaction: [
    { id: 1, name: '非常满意', value: 45, percentage: 45 },
    { id: 2, name: '满意', value: 35, percentage: 35 },
    { id: 3, name: '一般', value: 15, percentage: 15 },
    { id: 4, name: '不满意', value: 5, percentage: 5 }
  ],
  traffic: [
    { id: 1, name: '高速公路', value: 234567, percentage: 60 },
    { id: 2, name: '铁路', value: 156789, percentage: 25 },
    { id: 3, name: '航空', value: 78901, percentage: 15 }
  ],
  hotelOccupancy: [
    { id: 1, name: '五星级酒店', value: 85, percentage: 85 },
    { id: 2, name: '四星级酒店', value: 78, percentage: 78 },
    { id: 3, name: '三星级酒店', value: 65, percentage: 65 },
    { id: 4, name: '经济型酒店', value: 72, percentage: 72 }
  ],
  weather: {
    city: '济南',
    temperature: 25,
    humidity: 65,
    weather: '晴',
    windSpeed: 12,
    airQuality: '良'
  },
  powerSystem: {
    totalPower: 211.4,
    aPhase: [10, 15, 25, 35, 45, 55, 70, 45, 25],
    bPhase: [45, 55, 50, 60, 55, 40, 35, 50, 45],
    cPhase: [25, 35, 45, 50, 35, 25, 20, 30, 15],
    timeData: ['00:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '18:00', '20:00']
  }
}

// 通用响应格式
const createResponse = (data, message = '成功') => ({
  code: 200,
  message,
  data,
  success: true
})

// API路由
app.get('/api/tourism/realtime', (req, res) => {
  res.json(createResponse(mockData.realtime))
})

app.get('/api/tourism/regions', (req, res) => {
  res.json(createResponse(mockData.regions))
})

app.get('/api/tourism/hotspots', (req, res) => {
  res.json(createResponse(mockData.hotspots))
})

app.get('/api/tourism/visitor-trend', (req, res) => {
  res.json(createResponse(mockData.visitorTrend))
})

app.get('/api/tourism/revenue-stats', (req, res) => {
  res.json(createResponse(mockData.revenueStats))
})

app.get('/api/tourism/satisfaction', (req, res) => {
  res.json(createResponse(mockData.satisfaction))
})

app.get('/api/tourism/traffic', (req, res) => {
  res.json(createResponse(mockData.traffic))
})

app.get('/api/tourism/hotel-occupancy', (req, res) => {
  res.json(createResponse(mockData.hotelOccupancy))
})

app.get('/api/weather/current', (req, res) => {
  const { city } = req.query
  const weatherData = { ...mockData.weather, city: city || '济南' }
  res.json(createResponse(weatherData))
})

app.get('/api/power/system-data', (req, res) => {
  res.json(createResponse(mockData.powerSystem))
})

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: null,
    success: false
  })
})

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: null,
    success: false
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 模拟API服务器已启动`)
  console.log(`📍 服务地址: http://localhost:${PORT}`)
  console.log(`📋 API文档: http://localhost:${PORT}/api`)
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`)
})
