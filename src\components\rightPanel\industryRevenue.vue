<!-- 设备统计 -->
<template>
  <CPanel>
    <template #header>设备统计</template>
    <template #content>
      <div class="equipment-stats">
        <!-- 设备类型统计 -->
        <div class="equipment-types">
          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="地理侧循环泵" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">地理侧循环泵</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                14个
              </div>
            </div>
          </div>

          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="补水泵" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">补水泵</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                21个
              </div>
            </div>
          </div>

          <div class="equipment-item">
            <div class="equipment-icon">
              <img src="@/assets/img/水泵icon.png" alt="机组" />
            </div>
            <div class="equipment-info">
              <div class="equipment-name">机组</div>
              <div class="equipment-count" :style="{ backgroundImage: 'url(' + dyTitleImg + ')' }">
                12个
              </div>
            </div>
          </div>
        </div>

        <!-- 设备状态标题 -->
        <div class="status-title" :style="{ backgroundImage: 'url(' + nSmallTitleImg + ')' }">
           设备状态
        </div>

        <!-- 设备状态统计 -->
        <div class="equipment-status">
          <!-- 左侧统计 -->
          <div class="status-left">
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbLeftImg + ')' }">
              <div class="status-label">设备总数</div>
              <div class="status-value">124</div>
            </div>
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbLeftImg + ')' }">
              <div class="status-label">在线</div>
              <div class="status-value">12</div>
            </div>
          </div>

          <!-- 中间图片 -->
          <div class="status-center">
            <img src="@/assets/img/sb_center.png" alt="设备中心图" />
          </div>

          <!-- 右侧统计 -->
          <div class="status-right">
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbRightImg + ')' }">
              <div class="status-label">离线</div>
              <div class="status-value">4</div>
            </div>
            <div class="status-item" :style="{ backgroundImage: 'url(' + sbRightImg + ')' }">
              <div class="status-label">故障</div>
              <div class="status-value">23</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
import dyTitleImg from '@/assets/img/dy_title.png'
import nSmallTitleImg from '@/assets/img/n_samllTitle.png'
import sbLeftImg from '@/assets/img/sb_left.png'
import sbRightImg from '@/assets/img/sb_right.png'
</script>

<style lang="scss" scoped>
.equipment-stats {
  padding: 10px;
  color: #fff;
  background: url('@/assets/img/设备统计底框.png') no-repeat center center;
  background-size: 100% 100%;
}

.equipment-types {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 10px;
}

.equipment-item {
  display: flex;
  align-items: center;
  flex: 1;
}

.equipment-icon {
  margin-right: 8px;

  img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: 2px solid rgba(147, 185, 255, 0.5);
    padding: 5px;
    background: rgba(147, 185, 255, 0.1);
  }
}

.equipment-info {
  flex: 1;
}

.equipment-name {
  font-size: 12px;
  color: #C5D6E6;
  margin-bottom: 4px;
}

.equipment-count {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 4px 10px;
  text-align: center;
  min-height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-title {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding-left: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  color: #fff;
  display: flex;
  align-items: center;
  height: 40px;
  width: 100%;

  .diamond {
    color: #4ECDC4;
    margin-right: 6px;
    font-size: 10px;
  }
}

.equipment-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.status-left,
.status-right {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.status-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 80px;
    max-height: 80px;
  }
}

.status-item {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  padding: 8px 12px;
  text-align: center;
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.status-label {
  font-size: 12px;
  color: #C5D6E6;
  margin-bottom: 2px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
</style>
