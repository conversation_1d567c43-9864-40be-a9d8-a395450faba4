/**
 * API接口统一管理
 */

import request, { ApiResponse } from '@/utils/request'

// 旅游数据相关接口
export interface TourismData {
  id: number
  name: string
  value: number
  percentage?: number
  trend?: 'up' | 'down' | 'stable'
}

// 实时数据接口
export interface RealTimeData {
  totalVisitors: number
  todayVisitors: number
  revenue: number
  satisfaction: number
  timestamp: string
}

// 地区数据接口
export interface RegionData {
  region: string
  visitors: number
  revenue: number
  growth: number
}

// 热门景点数据接口
export interface HotSpotData {
  name: string
  visitors: number
  rating: number
  location: string
}

/**
 * 获取实时旅游数据
 */
export const getRealTimeData = (): Promise<ApiResponse<RealTimeData>> => {
  return request.get('/tourism/realtime')
}

/**
 * 获取地区旅游数据
 */
export const getRegionData = (): Promise<ApiResponse<RegionData[]>> => {
  return request.get('/tourism/regions')
}

/**
 * 获取热门景点数据
 */
export const getHotSpots = (): Promise<ApiResponse<HotSpotData[]>> => {
  return request.get('/tourism/hotspots')
}

/**
 * 获取游客流量趋势数据
 */
export const getVisitorTrend = (params?: {
  startDate?: string
  endDate?: string
  type?: 'daily' | 'weekly' | 'monthly'
}): Promise<ApiResponse<TourismData[]>> => {
  return request.get('/tourism/visitor-trend', { params })
}

/**
 * 获取收入统计数据
 */
export const getRevenueStats = (params?: {
  startDate?: string
  endDate?: string
}): Promise<ApiResponse<TourismData[]>> => {
  return request.get('/tourism/revenue-stats', { params })
}

/**
 * 获取满意度调查数据
 */
export const getSatisfactionData = (): Promise<ApiResponse<TourismData[]>> => {
  return request.get('/tourism/satisfaction')
}

/**
 * 获取交通数据
 */
export const getTrafficData = (): Promise<ApiResponse<TourismData[]>> => {
  return request.get('/tourism/traffic')
}

/**
 * 获取酒店入住率数据
 */
export const getHotelOccupancy = (): Promise<ApiResponse<TourismData[]>> => {
  return request.get('/tourism/hotel-occupancy')
}

/**
 * 获取天气数据
 */
export const getWeatherData = (city?: string): Promise<ApiResponse<any>> => {
  return request.get('/weather/current', { params: { city } })
}

/**
 * 获取电力系统数据
 */
export const getPowerSystemData = (): Promise<ApiResponse<any>> => {
  return request.get('/power/system-data')
}

// 导出所有API
export default {
  getRealTimeData,
  getRegionData,
  getHotSpots,
  getVisitorTrend,
  getRevenueStats,
  getSatisfactionData,
  getTrafficData,
  getHotelOccupancy,
  getWeatherData,
  getPowerSystemData
}
