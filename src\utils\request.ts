/**
 * HTTP请求工具
 * 基于axios封装的请求工具，包含请求拦截器、响应拦截器等
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { getApiBaseUrl, getApiTimeout, isDev } from './env'

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
}

class HttpRequest {
  private instance: AxiosInstance

  constructor() {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: getApiBaseUrl(),
      timeout: getApiTimeout(),
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })

    // 设置请求拦截器
    this.setRequestInterceptors()
    
    // 设置响应拦截器
    this.setResponseInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  private setRequestInterceptors(): void {
    this.instance.interceptors.request.use(
      (config) => {
        // 在发送请求之前做些什么
        if (isDev()) {
          console.log('🚀 请求发送:', config)
        }

        // 可以在这里添加token
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        return config
      },
      (error: AxiosError) => {
        // 对请求错误做些什么
        if (isDev()) {
          console.error('❌ 请求错误:', error)
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * 设置响应拦截器
   */
  private setResponseInterceptors(): void {
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 对响应数据做点什么
        if (isDev()) {
          console.log('✅ 响应接收:', response)
        }

        const { data } = response
        
        // 根据业务需求处理响应数据
        if (data.code === 200 || data.success) {
          return data
        } else {
          // 业务错误处理
          this.handleBusinessError(data)
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      },
      (error: AxiosError) => {
        // 对响应错误做点什么
        if (isDev()) {
          console.error('❌ 响应错误:', error)
        }
        
        this.handleHttpError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 获取token
   */
  private getToken(): string | null {
    // 从localStorage或其他地方获取token
    return localStorage.getItem('token') || null
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(data: ApiResponse): void {
    const { code, message } = data
    
    switch (code) {
      case 401:
        // 未授权，清除token并跳转到登录页
        localStorage.removeItem('token')
        // 这里可以添加路由跳转逻辑
        console.warn('用户未授权，请重新登录')
        break
      case 403:
        console.warn('权限不足')
        break
      case 404:
        console.warn('请求的资源不存在')
        break
      case 500:
        console.error('服务器内部错误')
        break
      default:
        console.error(`业务错误: ${message}`)
    }
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(error: AxiosError): void {
    const { response, message } = error
    
    if (response) {
      const { status, statusText } = response
      console.error(`HTTP错误 ${status}: ${statusText}`)
      
      switch (status) {
        case 400:
          console.error('请求参数错误')
          break
        case 401:
          console.error('未授权访问')
          break
        case 403:
          console.error('禁止访问')
          break
        case 404:
          console.error('请求地址不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        case 502:
          console.error('网关错误')
          break
        case 503:
          console.error('服务不可用')
          break
        default:
          console.error(`HTTP错误: ${status}`)
      }
    } else {
      // 网络错误
      if (message.includes('timeout')) {
        console.error('请求超时')
      } else if (message.includes('Network Error')) {
        console.error('网络连接错误')
      } else {
        console.error('请求失败')
      }
    }
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.get(url, config)
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config)
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config)
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config)
  }

  /**
   * PATCH请求
   */
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.instance.patch(url, data, config)
  }
}

// 创建请求实例
const request = new HttpRequest()

export default request
